@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Color Palette */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* Primary - Modern Blue/Purple Gradient */
    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 262 83% 52%;
    --primary-light: 262 83% 95%;

    /* Secondary - Sophisticated Gray */
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --secondary-hover: 240 4.8% 90%;

    /* Accent - Vibrant <PERSON>l */
    --accent: 173 80% 40%;
    --accent-foreground: 0 0% 98%;
    --accent-hover: 173 80% 35%;
    --accent-light: 173 80% 95%;

    /* Success - Modern Green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --success-light: 142 76% 95%;

    /* Warning - Warm Orange */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --warning-light: 38 92% 95%;

    /* Error - Modern Red */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --destructive-light: 0 84.2% 95%;

    /* Neutral Grays */
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 262 83% 58%;

    /* Surface Colors */
    --surface: 0 0% 99%;
    --surface-hover: 240 4.8% 95.9%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(262 83% 58%) 0%, hsl(173 80% 40%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(240 4.8% 95.9%) 0%, hsl(0 0% 100%) 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Border Radius */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Primary - Brighter in dark mode */
    --primary: 262 83% 65%;
    --primary-foreground: 240 10% 3.9%;
    --primary-hover: 262 83% 70%;
    --primary-light: 262 83% 15%;

    /* Secondary - Dark gray */
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --secondary-hover: 240 3.7% 20%;

    /* Accent - Brighter teal */
    --accent: 173 80% 50%;
    --accent-foreground: 240 10% 3.9%;
    --accent-hover: 173 80% 55%;
    --accent-light: 173 80% 15%;

    /* Success */
    --success: 142 76% 45%;
    --success-foreground: 240 10% 3.9%;
    --success-light: 142 76% 15%;

    /* Warning */
    --warning: 38 92% 60%;
    --warning-foreground: 240 10% 3.9%;
    --warning-light: 38 92% 15%;

    /* Error */
    --destructive: 0 84.2% 65%;
    --destructive-foreground: 240 10% 3.9%;
    --destructive-light: 0 84.2% 15%;

    /* Neutral Grays */
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 262 83% 65%;

    /* Surface Colors */
    --surface: 240 10% 6%;
    --surface-hover: 240 3.7% 15.9%;

    /* Gradients for dark mode */
    --gradient-primary: linear-gradient(135deg, hsl(262 83% 65%) 0%, hsl(173 80% 50%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(240 3.7% 15.9%) 0%, hsl(240 10% 3.9%) 100%);

    /* Shadows for dark mode */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  /* Typography Scale */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
    line-height: 1.2;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
    font-weight: 700;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
    font-weight: 600;
  }

  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl;
    font-weight: 600;
  }

  h4 {
    @apply text-xl md:text-2xl lg:text-3xl;
    font-weight: 600;
  }

  h5 {
    @apply text-lg md:text-xl lg:text-2xl;
    font-weight: 600;
  }

  h6 {
    @apply text-base md:text-lg lg:text-xl;
    font-weight: 600;
  }

  /* Code Typography */
  code, pre {
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  }

  /* Focus States */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Selection */
  ::selection {
    background: hsl(var(--primary) / 0.2);
    color: hsl(var(--primary-foreground));
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
}

@layer components {
  /* Button Variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary-hover shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary-hover border border-border;
  }

  .btn-accent {
    @apply bg-accent text-accent-foreground hover:bg-accent-hover shadow-md hover:shadow-lg;
  }

  .btn-ghost {
    @apply hover:bg-accent/10 hover:text-accent;
  }

  .btn-outline {
    @apply border border-border bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-md hover:shadow-lg;
  }

  /* Button Sizes */
  .btn-sm {
    @apply h-9 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-11 px-8 text-base;
  }

  .btn-xl {
    @apply h-12 px-10 text-lg;
  }

  /* Card Components */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .card-hover {
    @apply transition-all duration-200 hover:shadow-md hover:-translate-y-1;
  }

  .card-interactive {
    @apply cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-2 hover:border-primary/20;
  }

  /* Input Components */
  .input {
    @apply flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .input-lg {
    @apply h-12 px-4 text-base;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary-hover;
  }

  .badge-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary-hover;
  }

  .badge-accent {
    @apply bg-accent text-accent-foreground hover:bg-accent-hover;
  }

  .badge-outline {
    @apply border border-border text-foreground hover:bg-accent hover:text-accent-foreground;
  }

  /* Gradient Backgrounds */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  /* Glass Effect */
  .glass {
    @apply backdrop-blur-md bg-background/80 border border-border/50;
  }

  /* Loading States */
  .loading {
    @apply animate-pulse;
  }

  .skeleton {
    @apply bg-muted rounded animate-pulse;
  }
}

@layer utilities {
  /* Text Utilities */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  /* Hover Effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-lg hover:shadow-primary/25;
  }

  /* Layout Utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-12 md:py-16 lg:py-20;
  }

  /* Responsive Text */
  .text-responsive-sm {
    @apply text-sm md:text-base;
  }

  .text-responsive-base {
    @apply text-base md:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  .text-responsive-3xl {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom Scrollbar for specific elements */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.2);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.4);
}