'use client';

import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Search, Menu, X, User, Settings, LogOut, Sparkles, Home, Compass, Plus, FolderOpen, Shield } from 'lucide-react';

export default function Navbar() {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigation = [
    { name: 'Home', href: '/', icon: Home },
    { name: 'Browse', href: '/browse', icon: Compass },
    { name: 'Categories', href: '/categories', icon: FolderOpen },
    { name: 'Create', href: '/create', icon: Plus },
  ];

  const authenticatedNavigation = [
    { name: 'My Prompts', href: '/my-prompts', icon: Sparkles },
  ];

  const adminNavigation = [
    { name: 'Admin', href: '/admin', icon: Shield },
  ];

  const userNavigation = [
    { name: 'Profile', href: '/profile', icon: User },
    { name: 'My Prompts', href: '/my-prompts', icon: Sparkles },
    { name: 'Settings', href: '/settings', icon: Settings },
  ];

  const isActive = (href: string) => {
    if (!pathname) return false;
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-background/80 backdrop-blur-lg border-b border-border/50 shadow-lg'
        : 'bg-background/95 backdrop-blur-sm border-b border-border/30'
    }`}>
      <div className="container-custom">
        <div className="flex justify-between items-center h-16 lg:h-18">
          {/* Logo */}
          <div className="flex items-center space-x-4">
            <Link
              href="/"
              className="flex items-center space-x-2 group"
            >
              <div className="relative">
                <div className="w-8 h-8 rounded-lg bg-gradient-primary flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div className="absolute -inset-1 bg-gradient-primary rounded-lg opacity-0 group-hover:opacity-20 transition-opacity blur"></div>
              </div>
              <span className="text-xl font-bold text-gradient hidden sm:block">
                AI Prompt Library
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActive(item.href)
                      ? 'bg-primary text-primary-foreground shadow-md'
                      : 'text-muted-foreground hover:text-foreground hover:bg-surface-hover'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}

            {session && authenticatedNavigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActive(item.href)
                      ? 'bg-primary text-primary-foreground shadow-md'
                      : 'text-muted-foreground hover:text-foreground hover:bg-surface-hover'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}

            {session && (session.user?.role === 'admin' || session.user?.role === 'moderator') && adminNavigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActive(item.href)
                      ? 'bg-primary text-primary-foreground shadow-md'
                      : 'text-muted-foreground hover:text-foreground hover:bg-surface-hover'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </div>

          {/* Right side - User menu */}
          <div className="flex items-center space-x-4">
            {status === 'loading' ? (
              <div className="flex items-center space-x-2">
                <div className="skeleton h-8 w-8 rounded-full"></div>
                <div className="skeleton h-4 w-16 rounded hidden sm:block"></div>
              </div>
            ) : session ? (
              <div className="flex items-center space-x-3">
                {/* User dropdown */}
                <div className="relative">
                  <button
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                    className="flex items-center space-x-2 p-2 rounded-lg hover:bg-surface-hover transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  >
                    <div className="relative">
                      <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center shadow-md">
                        <span className="text-sm font-semibold text-white">
                          {session.user?.name?.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-success rounded-full border-2 border-background"></div>
                    </div>
                    <div className="hidden sm:block text-left">
                      <div className="text-sm font-medium text-foreground">{session.user?.name}</div>
                      <div className="text-xs text-muted-foreground">{session.user?.role}</div>
                    </div>
                  </button>

                  {/* Enhanced Dropdown menu */}
                  {isMenuOpen && (
                    <>
                      <div
                        className="fixed inset-0 z-40"
                        onClick={() => setIsMenuOpen(false)}
                      ></div>
                      <div className="absolute right-0 mt-2 w-64 bg-card rounded-xl shadow-xl border border-border z-50 animate-scale-in">
                        <div className="p-4 border-b border-border">
                          <div className="flex items-center space-x-3">
                            <div className="h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center">
                              <span className="text-sm font-semibold text-white">
                                {session.user?.name?.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <div className="font-medium text-foreground">{session.user?.name}</div>
                              <div className="text-sm text-muted-foreground">{session.user?.email}</div>
                              <div className="text-xs text-accent font-medium capitalize">{session.user?.role}</div>
                            </div>
                          </div>
                        </div>

                        <div className="p-2">
                          {userNavigation.map((item) => {
                            const Icon = item.icon;
                            return (
                              <Link
                                key={item.name}
                                href={item.href}
                                className="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm text-foreground hover:bg-surface-hover transition-colors"
                                onClick={() => setIsMenuOpen(false)}
                              >
                                <Icon className="w-4 h-4 text-muted-foreground" />
                                <span>{item.name}</span>
                              </Link>
                            );
                          })}

                          <div className="border-t border-border my-2"></div>

                          <button
                            onClick={() => {
                              setIsMenuOpen(false);
                              signOut();
                            }}
                            className="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm text-destructive hover:bg-destructive-light transition-colors w-full"
                          >
                            <LogOut className="w-4 h-4" />
                            <span>Sign out</span>
                          </button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link
                  href="/auth/signin"
                  className="btn btn-ghost btn-sm"
                >
                  Sign in
                </Link>
                <Link
                  href="/auth/signup"
                  className="btn btn-primary btn-sm"
                >
                  Sign up
                </Link>
              </div>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 rounded-lg hover:bg-surface-hover transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              <span className="sr-only">Open main menu</span>
              {isMenuOpen ? (
                <X className="h-5 w-5 text-foreground" />
              ) : (
                <Menu className="h-5 w-5 text-foreground" />
              )}
            </button>
          </div>

        </div>
      </div>

      {/* Enhanced Mobile menu */}
      {isMenuOpen && (
        <>
          <div
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-40 lg:hidden"
            onClick={() => setIsMenuOpen(false)}
          ></div>
          <div className="fixed top-16 left-0 right-0 bg-card border-b border-border shadow-xl z-50 lg:hidden animate-slide-down">
            <div className="container-custom py-6">
              {/* Navigation Links */}
              <div className="space-y-2 mb-6">
                {navigation.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-colors ${
                        isActive(item.href)
                          ? 'bg-primary text-primary-foreground shadow-md'
                          : 'text-foreground hover:bg-surface-hover'
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.name}</span>
                    </Link>
                  );
                })}

                {session && authenticatedNavigation.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-colors ${
                        isActive(item.href)
                          ? 'bg-primary text-primary-foreground shadow-md'
                          : 'text-foreground hover:bg-surface-hover'
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.name}</span>
                    </Link>
                  );
                })}

                {session && (session.user?.role === 'admin' || session.user?.role === 'moderator') && adminNavigation.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-colors ${
                        isActive(item.href)
                          ? 'bg-primary text-primary-foreground shadow-md'
                          : 'text-foreground hover:bg-surface-hover'
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.name}</span>
                    </Link>
                  );
                })}
              </div>

              {/* User Section */}
              {session ? (
                <div className="border-t border-border pt-6">
                  <div className="flex items-center space-x-4 px-4 mb-4">
                    <div className="h-12 w-12 rounded-full bg-gradient-primary flex items-center justify-center shadow-md">
                      <span className="text-lg font-semibold text-white">
                        {session.user?.name?.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <div className="text-base font-medium text-foreground">{session.user?.name}</div>
                      <div className="text-sm text-muted-foreground">{session.user?.email}</div>
                      <div className="text-xs text-accent font-medium capitalize">{session.user?.role}</div>
                    </div>
                  </div>

                  <div className="space-y-1">
                    {userNavigation.map((item) => {
                      const Icon = item.icon;
                      return (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="flex items-center space-x-3 px-4 py-2 rounded-lg text-sm text-foreground hover:bg-surface-hover transition-colors"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Icon className="w-4 h-4 text-muted-foreground" />
                          <span>{item.name}</span>
                        </Link>
                      );
                    })}

                    <button
                      onClick={() => {
                        setIsMenuOpen(false);
                        signOut();
                      }}
                      className="flex items-center space-x-3 px-4 py-2 rounded-lg text-sm text-destructive hover:bg-destructive-light transition-colors w-full"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>Sign out</span>
                    </button>
                  </div>
                </div>
              ) : (
                <div className="border-t border-border pt-6 space-y-3">
                  <Link
                    href="/auth/signin"
                    className="btn btn-outline btn-lg w-full"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign in
                  </Link>
                  <Link
                    href="/auth/signup"
                    className="btn btn-primary btn-lg w-full"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign up
                  </Link>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </nav>
  );
}